{"name": "spike-next-main", "version": "2.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "ts": "tsc", "lint": "next lint", "lint:fix": "yarn lint --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@casl/ability": "^6.3.3", "@casl/react": "^3.1.0", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.9.1", "@iconify/react": "^4.1.1", "@lexical/code": "^0.20.0", "@lexical/html": "^0.20.0", "@lexical/link": "^0.20.0", "@lexical/list": "^0.20.0", "@lexical/markdown": "^0.20.0", "@lexical/react": "^0.20.0", "@lexical/rich-text": "^0.20.0", "@lexical/table": "^0.20.0", "@mui/icons-material": "^5.15.18", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.18", "@mui/system": "^5.15.15", "@mui/utils": "^5.15.14", "@mui/x-date-pickers": "^7.22.1", "@mui/x-tree-view": "^6.17.0", "@next/third-parties": "^14.2.15", "@reduxjs/toolkit": "^1.9.7", "@sentry/nextjs": "^8", "@tabler/icons-react": "^2.39.0", "@types/react": "18.2.28", "@types/react-dom": "18.2.13", "apexcharts": "3.43.0", "axios": "^1.5.1", "axios-cache-interceptor": "^1.5.3", "axios-curlirize": "^2.0.0", "axios-mock-adapter": "^1.22.0", "browser-image-compression": "^2.0.2", "chance": "^1.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cupertino-pane": "^1.4.21", "date-fns": "^2.30.0", "date-fns-jalali": "^2.30.0-0", "dayjs": "^1.11.10", "dayjs-calendar-hijri": "^1.0.2", "devextreme": "^22.2.6", "devextreme-react": "^22.2.6", "emoji-picker-react": "^4.5.2", "formik": "^2.4.5", "formik-mui": "^5.0.0-alpha.0", "framer-motion": "^10.16.4", "froala-editor": "^4.5.2", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "i18next": "^23.5.1", "jalali-plugin-dayjs": "^1.1.4", "jalaliday": "^2.3.0", "js-cookie": "^3.0.5", "lexical": "^0.20.0", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "14.0.3", "nuqs": "^2.3.2", "path-to-regexp": "^6.2.1", "prom-client": "^15.1.3", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-big-calendar": "^1.8.4", "react-date-object": "^2.1.8", "react-dom": "^18.2.0", "react-froala-wysiwyg": "^4.5.2", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.54.2", "react-html2pdf": "^1.0.1", "react-i18next": "^13.2.2", "react-image-crop": "^11.0.7", "react-intersection-observer": "^9.5.2", "react-multi-date-picker": "^4.4.1", "react-otp-input": "^3.1.1", "react-perfect-scrollbar": "^1.5.8", "react-quill": "^2.0.0", "react-redux": "^8.1.3", "react-spring": "^9.7.3", "react-to-pdf": "^1.0.1", "react-to-print": "^2.15.1", "react-toastify": "^11.0.3", "react-tribute": "^1.0.4", "react-use": "^17.6.0", "react-virtualized": "^9.22.5", "react-virtualized-tree": "^3.4.1", "redux": "^4.2.1", "redux-persist": "^6.0.0", "sass": "^1.70.0", "sharp": "^0.32.6", "simplebar-react": "^3.2.4", "stylis-plugin-rtl": "^2.1.1", "swiper": "^11.1.12", "tailwind-merge": "^2.5.2", "@tinymce/tinymce-react": "3.5.0", "tinymce-react": "1.3.2", "tributejs": "^5.1.3", "ts-case-convert": "^2.0.7", "turndown": "^7.2.0", "uuid": "^9.0.1", "yup": "^0.32.11", "yup-phone": "^1.3.2", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@babel/generator": "^7.24.6", "@babel/parser": "^7.24.6", "@babel/traverse": "^7.24.6", "@storybook/addon-essentials": "^8.2.9", "@storybook/addon-interactions": "^8.2.9", "@storybook/addon-links": "^8.2.9", "@storybook/addon-onboarding": "^8.2.9", "@storybook/blocks": "^8.2.9", "@storybook/nextjs": "^8.2.9", "@storybook/react": "^8.2.9", "@storybook/test": "^8.2.9", "@types/chance": "^1.1.4", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.199", "@types/node": "^20.8.4", "@types/react": "18.2.28", "@types/react-big-calendar": "^1.6.5", "@types/turndown": "^5.0.5", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "autoprefixer": "^10.4.19", "eslint": "latest", "eslint-config-next": "latest", "eslint-config-prettier": "^9.0.0", "eslint-plugin-storybook": "^0.8.0", "husky": "^8.0.0", "lint-staged": "^14.0.0", "postcss": "^8.4.39", "prettier": "^3.0.2", "prettier-plugin-tailwindcss": "^0.6.6", "storybook": "^8.2.9", "tailwindcss": "^3.4.6", "typescript": "^5.3.3"}}