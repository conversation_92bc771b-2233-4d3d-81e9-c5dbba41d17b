/* eslint-disable @next/next/no-img-element */
/* eslint-disable jsx-a11y/alt-text */
import React from "react";
import { ImageUploaderChildrenReturnProps, TImageUploaderWithCropper } from "../types";
import { CircularProgress, MenuItem } from "@mui/material";
import { twMerge } from "tailwind-merge";
import { Icon } from "@iconify/react";
import { formatFileSize, isExcelType } from "../utils";
import ImageUploader from "../ImageUploader";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import Button from "@/components/ui/Button";

function RenderContent(props: ImageUploaderChildrenReturnProps & { isAcceptExcel?: boolean }) {
  const {
    isUploading,
    value,
    progressPercent,
    cancelUpload,
    uploadedFileSize,
    fileName,
    removeFile,
    isAcceptExcel,
    isLoading
  } = props;
  const { t } = useTranslation();

  const onClickCancel: React.MouseEventHandler<HTMLDivElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    cancelUpload();
  };

  const onClickRemove: React.MouseEventHandler<HTMLLIElement> = e => {
    e.preventDefault();
    e.stopPropagation();
    removeFile();
  };

  return (
    <div
      onClick={e => {
        if (isLoading || isUploading) {
          e.preventDefault();
          e.stopPropagation();
        }
      }}
    >
      {/* -------------------------------- isLoading ------------------------------- */}
      {isLoading && (
        <div className="flex items-center justify-center h-full w-full absolute left-0 top-0 bg-gray-999/20 backdrop-blur-sm z-10">
          <CircularProgress size={24} className="text-background" />
        </div>
      )}

      {/* ------------------------------- isUploading ------------------------------ */}

      {/* ------------------------- preview and select icon ------------------------ */}
      <div className={twMerge("py-4 px-4 rounded-lg border border-v2-border-secondary min-h-20 ")}>
        {(value || isUploading) && isAcceptExcel ? (
          <div className="flex justify-between">
            <div className="flex gap-3 items-start ">
              <Image src="/images/svgs/uploadedExcel.svg" width={32} height={32} alt="uploadedImage" />

              <div className="flex flex-col">
                <div className="flex items-center gap-1">
                  <p className="text-body3-medium xmd:max-w-[300px] max-w-[110px]">{fileName}</p>
                  {progressPercent === 100 && value && (
                    <Icon
                      icon="material-symbols:check-circle-rounded"
                      width={16}
                      height={16}
                      className="text-v2-surface-action"
                    />
                  )}
                </div>
                <span className="text-caption-medium">{uploadedFileSize ? formatFileSize(uploadedFileSize) : ""}</span>
              </div>
            </div>

            <Icon
              icon="solar:trash-bin-minimalistic-linear"
              width={16}
              height={16}
              className={twMerge("cursor-pointer")}
              onClick={e => {
                if (isUploading) {
                  onClickCancel(e as any);
                } else {
                  e.preventDefault();
                  e.stopPropagation();
                  onClickRemove(e as any);
                }
              }}
            />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Button
              variant="secondaryGray"
              startAdornment={<Icon icon="hugeicons:upload-square-02" className="text-v2-content-secondary size-4" />}
            >
              {t("product.excelUpload.uploadFile")}
            </Button>
          </div>
        )}

        {(isUploading || progressPercent > 1) && value && (
          <div className="flex items-center gap-4 mt-3">
            <div className="w-full h-2 bg-v2-surface-thertiary rounded-full overflow-hidden">
              <div
                className="h-full bg-v2-surface-action transition-all duration-300"
                style={{ width: `${progressPercent}%` }}
              ></div>
            </div>

            <span className="text-body3-medium">{progressPercent}%</span>
          </div>
        )}
      </div>
    </div>
  );
}

type TRectangleImageUploader = Omit<TImageUploaderWithCropper, "children"> & {
  hasCropper?: boolean;
};
function ExcelUploader({ hasCropper = true, ...restProps }: TRectangleImageUploader) {
  const isAcceptExcel = !restProps?.filePickerProps?.accept || isExcelType(restProps?.filePickerProps?.accept);

  //   if (isAcceptImage && hasCropper) {
  //     return (
  //       <ImageUploaderWithCropper {...restProps}>
  //         {returnProps => <RenderContent {...returnProps} isAcceptImage={isAcceptImage} />}
  //       </ImageUploaderWithCropper>
  //     );
  //   }

  return (
    <ImageUploader
      {...restProps}
      serverFileKind="document"
      filePickerProps={{
        disabled: !!restProps?.value,
        accept: restProps?.filePickerProps?.accept ?? ".xlsx,.xls,.csv"
      }}
    >
      {returnProps => <RenderContent {...returnProps} isAcceptExcel={isAcceptExcel} />}
    </ImageUploader>
  );
}

export default ExcelUploader;
