/* TinyMCE Editor Container Styles */
.tinymce-editor-container {
  position: relative;
  width: 100%;
}

.tox-statusbar {
  display: none !important;
}

.tox-menubar {
  display: none !important;
}

.tox-anchorbar {
  display: none !important;
}

.tox-editor-header {
  border-bottom: none !important;
}

.tox-tinymce {
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
}

/* Error state styling */
.tinymce-editor-container.tinymce-error .tox-editor-container {
  border: 2px solid #dc3545;
  border-radius: 4px;
}

.tinymce-editor-container.tinymce-error .tox-editor-header {
  border-bottom-color: #dc3545;
}

/* RTL Support */
.tinymce-editor-container[style*="direction: rtl"] .tox-toolbar,
.tinymce-editor-container[style*="direction: rtl"] .tox-menubar {
  direction: rtl;
}

.tinymce-editor-container[style*="direction: rtl"] .tox-toolbar__group {
  flex-direction: row-reverse;
}

/* Persian/Farsi font support */
.tinymce-editor-container[style*="direction: rtl"] .tox-edit-area iframe {
  font-family: <PERSON><PERSON><PERSON>, "Iranian Sans", "B Nazanin", <PERSON>l, sans-serif !important;
}

/* Custom toolbar styling */
.tox-toolbar__group {
  padding: 0 4px;
}

.tox-toolbar-textfield {
  direction: inherit;
}

/* Custom button styling */
.tox-tbtn--select {
  direction: inherit;
}

/* Editor content area */
.tox-edit-area {
  padding: 0;
}

/* Fullscreen mode */
.tox-fullscreen {
  z-index: 10000;
}

/* Custom styles for RTL content */
.tinymce-editor-container[style*="direction: rtl"] .tox-edit-area__iframe {
  direction: rtl;
  text-align: right;
}

/* Status bar */
.tox-statusbar {
  direction: inherit;
}

/* Menu styling for RTL */
.tox-menu {
  direction: inherit;
}

.tox-collection__item {
  direction: inherit;
}

/* Dialog styling */
.tox-dialog {
  direction: inherit;
}

.tox-form__group {
  direction: inherit;
}

.tox-textfield {
  direction: inherit;
  text-align: inherit;
}

/* Custom scrollbar for RTL */
.tinymce-editor-container[style*="direction: rtl"] .tox-sidebar-wrap {
  direction: rtl;
}

/* Color picker adjustments */
.tox-swatches {
  direction: ltr; /* Keep color swatches in LTR for consistency */
}

/* Table plugin RTL support */
.tox-pop__dialog {
  direction: inherit;
}

/* Image dialog RTL support */
.tox-form__controls-h-stack {
  direction: inherit;
}

/* Advanced code sample styling */
.tox-form__group .tox-listbox {
  direction: inherit;
}

/* Responsive design */
@media (max-width: 768px) {
  .tox-toolbar-scrollbar {
    display: block;
  }

  .tox-toolbar__group {
    flex-wrap: wrap;
  }
}

/* Custom Persian calendar support (if needed) */
.tinymce-editor-container[style*="direction: rtl"] .tox-form__group--stretched {
  direction: rtl;
}

/* Accessibility improvements */
.tox-toolbar__group button:focus,
.tox-tbtn:focus {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

/* Loading state */
.tinymce-editor-container .tox-throbber {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Custom styles for different editor themes */
.tinymce-editor-container .tox-silver-sink {
  direction: inherit;
}

/* Print styles */
@media print {
  .tox-toolbar,
  .tox-menubar,
  .tox-statusbar {
    display: none !important;
  }

  .tox-edit-area {
    border: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tox-toolbar,
  .tox-menubar {
    border: 2px solid;
  }

  .tox-tbtn {
    border: 1px solid;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tox:not(.tox-tinymce-inline) .tox-editor-header {
    background-color: #2f3349;
    border-bottom: 1px solid #e5e7eb;
  }

  .tox:not(.tox-tinymce-inline) .tox-editor-container {
    background-color: #2f3349;
  }
}
